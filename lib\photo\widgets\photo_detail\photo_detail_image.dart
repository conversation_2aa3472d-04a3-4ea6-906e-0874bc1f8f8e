import 'package:flutter/material.dart';
import 'package:portraitmode/app/widgets/pm_network_image.dart';
import 'package:portraitmode/image_pinch_zooming/image_pinch_zooming.dart';

class PhotoDetailImage extends StatelessWidget {
  const PhotoDetailImage({
    super.key,
    required this.photoUrl,
    required this.photoUrlLarge,
    required this.height,
    this.zoomable = false,
    this.onTwoFingersOn,
    this.onTwoFingersOff,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
  });

  final String photoUrl;
  final String photoUrlLarge;
  final double height;
  final bool zoomable;
  final Function? onTwoFingersOn;
  final Function? onTwoFingersOff;
  final Function? onTap;
  final Function? onDoubleTap;
  final Function? onLongPress;

  @override
  Widget build(BuildContext context) {
    return (zoomable ? _buildZoomablePhoto() : _buildUnzoomablePhoto());
  }

  Widget _buildZoomablePhoto() {
    return ImagePinchZooming(
      image: _buildPhoto(),
      // hideStatusBarWhileZooming: true,
      twoFingersOn: () {
        onTwoFingersOn?.call();
      },
      twoFingersOff: () {
        onTwoFingersOff?.call();
      },
      onTap: () {
        onTap?.call();
      },
      onDoubleTap: () {
        onDoubleTap?.call();
      },
    );
  }

  Widget _buildUnzoomablePhoto() {
    return GestureDetector(
      onTap: () {
        onTap?.call();
      },
      child: _buildPhoto(),
    );
  }

  Widget _buildPhoto() {
    return PmNetworkImage(
      url: photoUrlLarge,
      alignment: const Alignment(-1.0, -1.0),
      loadingWidget: PmNetworkImage(
        url: photoUrl,
        alignment: const Alignment(-1.0, -1.0),
      ),
    );
  }
}
