import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

class IsLikedWatcher extends ConsumerWidget {
  const IsLikedWatcher({
    super.key,
    required this.photoId,
    required this.isLiked,
  });

  final int photoId;
  final bool isLiked;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    bool? isLiked = ref.watch(photoProvider(photoId)).isLiked;

    return isLiked != null && isLiked
        ? const SizedBox.shrink()
        : const SizedBox.shrink();
  }
}
