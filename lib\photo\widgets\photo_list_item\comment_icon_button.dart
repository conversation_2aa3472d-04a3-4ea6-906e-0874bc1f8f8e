import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/comment/widgets/comments_bottom_sheet.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

class CommentIconButton extends ConsumerStatefulWidget {
  final PhotoData photo;
  final String screenName;
  final bool showCount;
  final bool shrinkTapTarget;

  const CommentIconButton({
    super.key,
    required this.photo,
    required this.screenName,
    this.showCount = false,
    this.shrinkTapTarget = false,
  });

  @override
  CommentIconButtonState createState() => CommentIconButtonState();
}

class CommentIconButtonState extends ConsumerState<CommentIconButton> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // log('[photo id: ${widget.photo.id}]: Building CommentIconButton');

    final int totalComments = !widget.showCount
        ? 0
        : (ref.watch(photoProvider(widget.photo.id)).totalComments ??
              widget.photo.totalComments);

    Color textColor = context.colors.brandColor;

    return Row(
      children: [
        IconButton(
          onPressed: () {
            _showCommentsBottomSheet();
          },
          padding: const EdgeInsets.all(0.0),
          iconSize: 22.0,
          icon: const Icon(Ionicons.chatbubble_outline),
          color: context.colors.iconColor,
          style: widget.shrinkTapTarget
              ? IconButton.styleFrom(
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  minimumSize: Size.zero,
                  padding: EdgeInsets.zero,
                )
              : null,
        ),
        if (widget.showCount) const SizedBox(width: 3.0),
        if (widget.showCount)
          Text(
            totalComments.toString(),
            style: TextStyle(
              color: textColor,
              fontSize: 11.0,
              fontWeight: FontWeight.w600,
            ),
          ),
      ],
    );
  }

  void _showCommentsBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return CommentsBottomSheet(photo: widget.photo);
      },
    );
  }
}
