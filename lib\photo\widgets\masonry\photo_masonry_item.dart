// Core packages
import 'package:flutter/material.dart';

// Extension packages

// Custom packages
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/widgets/photo_list_item/photo_frame.dart';
import 'package:portraitmode/photo/widgets/watch_helper/is_liked_watcher.dart';

class PhotoMasonryItem extends StatelessWidget {
  const PhotoMasonryItem({
    super.key,
    required this.index,
    this.containerWidth,
    required this.photo,
    this.isOwnProfile = false,
    required this.screenName,
    this.onTwoFingersOn,
    this.onTwoFingersOff,
    this.isSearchScreen = false,
    this.onPhotoTap,
  });

  final int index;
  final double? containerWidth;
  final PhotoData photo;
  final bool isOwnProfile;
  final String screenName;
  final Function? onTwoFingersOn;
  final Function? onTwoFingersOff;
  final bool isSearchScreen;
  final Function? onPhotoTap;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        PhotoFrame(
          containerWidth: containerWidth,
          isRounded: true,
          photo: photo,
          isOwnPhoto: isOwnProfile,
          screenName: screenName,
          isSmall: true,
          showMetadata: false,
          zoomable: false,
          onTwoFingersOn: onTwoFingersOn,
          onTwoFingersOff: onTwoFingersOff,
          onTap: onPhotoTap,
        ),
        // Because the masonry layout doesn't display the like button,
        // we need to watch the isLiked state in order the liking to work
        // from photo detail screen back and forward.
        IsLikedWatcher(photoId: photo.id, isLiked: photo.isLiked),
      ],
    );
  }
}
