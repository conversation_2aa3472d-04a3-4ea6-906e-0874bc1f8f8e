import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

final class SearchPhotoIdsNotifier extends IdListNotifier {}

final searchPhotoIdsProvider =
    NotifierProvider.autoDispose<SearchPhotoIdsNotifier, List<int>>(
      SearchPhotoIdsNotifier.new,
    );

final searchPhotosProvider = Provider.autoDispose<List<PhotoData>>((ref) {
  final ids = ref.watch(searchPhotoIdsProvider);
  final store = ref.watch(photoStoreProvider);
  return ids.map((id) => store[id]).whereType<PhotoData>().toList();
});
