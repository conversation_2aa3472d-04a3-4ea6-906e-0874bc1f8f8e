import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

final class FollowingPhotoIdsNotifier extends IdListNotifier {}

final followingPhotoIdsProvider =
    NotifierProvider.autoDispose<FollowingPhotoIdsNotifier, List<int>>(
      FollowingPhotoIdsNotifier.new,
    );

final followingPhotosProvider = Provider.autoDispose<List<PhotoData>>((ref) {
  final ids = ref.watch(followingPhotoIdsProvider);
  final store = ref.watch(photoStoreProvider);
  return ids.map((id) => store[id]).whereType<PhotoData>().toList();
});
