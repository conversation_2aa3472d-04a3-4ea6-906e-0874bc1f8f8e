import 'package:flutter/material.dart';
import 'package:portraitmode/app/widgets/pm_network_image.dart';
import 'package:portraitmode/image_pinch_zooming/image_pinch_zooming.dart';

class PhotoImage extends StatelessWidget {
  const PhotoImage({
    super.key,
    required this.photoUrl,
    required this.height,
    this.zoomable = false,
    this.onTwoFingersOn,
    this.onTwoFingersOff,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
  });

  final String photoUrl;
  final double height;
  final bool zoomable;
  final Function? onTwoFingersOn;
  final Function? onTwoFingersOff;
  final Function? onTap;
  final Function? onDoubleTap;
  final Function? onLongPress;

  @override
  Widget build(BuildContext context) {
    return (zoomable ? _buildZoomablePhoto() : _buildUnzoomablePhoto());
  }

  Widget _buildZoomablePhoto() {
    return ImagePinchZooming(
      image: _buildPhoto(),
      // hideStatusBarWhileZooming: true,
      twoFingersOn: () {
        onTwoFingersOn?.call();
      },
      twoFingersOff: () {
        onTwoFingersOff?.call();
      },
      onTap: () {
        onTap?.call();
      },
      onDoubleTap: () {
        onDoubleTap?.call();
      },
      onLongPress: () {
        onLongPress?.call();
      },
    );
  }

  Widget _buildUnzoomablePhoto() {
    return GestureDetector(
      onTap: () {
        onTap?.call();
      },
      onDoubleTap: () {
        onDoubleTap?.call();
      },
      onLongPress: () {
        onLongPress?.call();
      },
      child: _buildPhoto(),
    );
  }

  Widget _buildPhoto() {
    return PmNetworkImage(
      url: photoUrl,
      height: height,
      alignment: const Alignment(-1.0, -1.0),
    );
  }
}
