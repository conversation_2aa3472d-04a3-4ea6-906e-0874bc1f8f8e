import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';

final class ArtistStoreNotifier extends Notifier<List<ArtistData>> {
  final Map<int, int> _idIndexCache = {};

  @override
  List<ArtistData> build() => [];

  void _updateState(List<ArtistData> newState) {
    state = newState;
    _rebuildIndexCache();
  }

  void _rebuildIndexCache() {
    _idIndexCache.clear();
    for (var i = 0; i < state.length; i++) {
      _idIndexCache[state[i].id] = i;
    }
  }

  ArtistData? getItem(int id) {
    final index = _idIndexCache[id];
    if (index == null) return null;
    return state[index];
  }

  int getIndex(int id) {
    return _idIndexCache[id] ?? -1;
  }

  bool hasItem(int id) {
    return _idIndexCache.containsKey(id);
  }

  /// Uniquely adds a new item.
  /// For photo_store_provider, the order doesn't matter.
  void addItem(ArtistData newItem, {bool updateIfExists = true}) {
    if (hasItem(newItem.id)) {
      // Call updateItem instead of manually writing the update code here.
      if (updateIfExists) updateItem(newItem);
      return;
    }

    _updateState([...state, newItem]);
  }

  /// Uniquely adds a list of new items.
  /// For photo_store_provider, the order doesn't matter.
  ///
  /// Don't utilize `addItem` method to avoid unnecessary checking.
  void addItems(List<ArtistData> newItems, {bool updateIfExists = true}) {
    final existingIds = _idIndexCache.keys.toSet();

    List<ArtistData> parsedItems = [...state];

    if (updateIfExists) {
      final existingItemsToUpdate = newItems
          .where((item) => existingIds.contains(item.id))
          .toList();

      if (existingItemsToUpdate.isNotEmpty) {
        for (final item in existingItemsToUpdate) {
          final index = _idIndexCache[item.id]!;
          parsedItems[index] = item;
        }
      }
    }

    final newItemsToAdd = newItems
        .where((item) => !existingIds.contains(item.id))
        .toList();

    if (newItemsToAdd.isNotEmpty) {
      parsedItems.addAll(newItemsToAdd);
    }

    _updateState(parsedItems);
  }

  /// Updates an existing item.
  void updateItem(ArtistData newItem, {bool addIfNotExists = true}) {
    final index = _idIndexCache[newItem.id];

    if (index == null) {
      if (addIfNotExists) {
        // Just update directly, no need to call addItem here.
        _updateState([...state, newItem]);
      }

      return;
    }

    // If the item is the same, do nothing.
    if (state[index] == newItem) return;

    final updated = [...state];
    updated[index] = newItem;
    _updateState(updated);
  }

  /// Updates a list of existing items.
  ///
  /// Doesn't utilize `updateItem` method to avoid unnecessary checking.
  void updateItems(List<ArtistData> newItems, {bool addIfNotExists = true}) {
    final existingIds = _idIndexCache.keys.toSet();

    List<ArtistData> parsedItems = [...state];

    final existingItemsToUpdate = newItems
        .where((item) => existingIds.contains(item.id))
        .toList();

    if (existingItemsToUpdate.isNotEmpty) {
      for (final item in existingItemsToUpdate) {
        final index = _idIndexCache[item.id]!;
        parsedItems[index] = item;
      }
    }

    if (addIfNotExists) {
      final newItemsToAdd = newItems
          .where((item) => !existingIds.contains(item.id))
          .toList();

      if (newItemsToAdd.isNotEmpty) {
        parsedItems.addAll(newItemsToAdd);
      }
    }

    _updateState(parsedItems);
  }

  void removeItem(int id) {
    if (!_idIndexCache.containsKey(id)) return;
    final updated = state.where((item) => item.id != id).toList();
    _updateState(updated);
  }

  void replaceAll(List<ArtistData> newList) {
    _updateState(newList);
  }

  void clear() {
    _updateState([]);
  }

  // ------------------------------------------------------------
  // Additional methods tailored for artist_store_provider
  // ------------------------------------------------------------

  void setIsFollowing(int artistId, bool isFollowing) {
    final index = _idIndexCache[artistId];
    if (index == null) return;

    final updated = [...state];
    updated[index] = updated[index].copyWith(isFollowing: isFollowing);
    _updateState(updated);
  }

  void setTotalFollowing(int artistId, int totalFollowing) {
    final index = _idIndexCache[artistId];
    if (index == null) return;

    final updated = [...state];
    updated[index] = updated[index].copyWith(totalFollowing: totalFollowing);
    _updateState(updated);
  }

  void incrementTotalFollowing(int artistId) {
    final index = _idIndexCache[artistId];
    if (index == null) return;

    final updated = [...state];

    updated[index] = updated[index].copyWith(
      totalFollowing: updated[index].totalFollowing + 1,
    );

    _updateState(updated);
  }

  void decrementTotalFollowing(int artistId) {
    final index = _idIndexCache[artistId];
    if (index == null) return;

    final updated = [...state];
    final int updatedTotalFollowing = updated[index].totalFollowing - 1;

    updated[index] = updated[index].copyWith(
      totalFollowing: updatedTotalFollowing < 0 ? 0 : updatedTotalFollowing,
    );

    _updateState(updated);
  }

  void setTotalFollowers(int artistId, int totalFollowers) {
    final index = _idIndexCache[artistId];
    if (index == null) return;

    final updated = [...state];
    updated[index] = updated[index].copyWith(totalFollowers: totalFollowers);
    _updateState(updated);
  }

  void incrementTotalFollowers(int artistId) {
    final index = _idIndexCache[artistId];
    if (index == null) return;

    final updated = [...state];

    updated[index] = updated[index].copyWith(
      totalFollowers: updated[index].totalFollowers + 1,
    );

    _updateState(updated);
  }

  void decrementTotalFollowers(int artistId) {
    final index = _idIndexCache[artistId];
    if (index == null) return;

    final updated = [...state];
    final updatedTotalFollowers = updated[index].totalFollowers - 1;

    updated[index] = updated[index].copyWith(
      totalFollowers: updatedTotalFollowers < 0 ? 0 : updatedTotalFollowers,
    );

    _updateState(updated);
  }

  void setIsBlocked(int artistId, bool isBlocked) {
    final index = _idIndexCache[artistId];
    if (index == null) return;

    final updated = [...state];
    updated[index] = updated[index].copyWith(isBlocked: isBlocked);
    _updateState(updated);
  }

  void setIsBlocking(int artistId, bool isBlocking) {
    final index = _idIndexCache[artistId];
    if (index == null) return;

    final updated = [...state];
    updated[index] = updated[index].copyWith(isBlocking: isBlocking);
    _updateState(updated);
  }
}

final artistStoreProvider =
    NotifierProvider.autoDispose<ArtistStoreNotifier, List<ArtistData>>(
      ArtistStoreNotifier.new,
    );

final artistProvider =
    Provider.family<
      ({
        bool isFollowing,
        int totalFollowing,
        int totalFollowers,
        bool isBlocked,
        bool isBlocking,
      }),
      int
    >((ref, artistId) {
      final artists = ref.watch(artistStoreProvider);
      final artist = artists.firstWhere(
        (a) => a.id == artistId,
        orElse: () => const ArtistData(), // or handle null gracefully
      );

      return (
        isFollowing: artist.isFollowing,
        totalFollowing: artist.totalFollowing,
        totalFollowers: artist.totalFollowers,
        isBlocked: artist.isBlocked,
        isBlocking: artist.isBlocking,
      );
    });
