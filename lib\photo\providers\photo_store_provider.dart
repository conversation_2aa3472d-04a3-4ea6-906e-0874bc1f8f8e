// photo_store_provider.dart

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/comment/providers/comment_list_provider.dart';
import 'package:portraitmode/comment/providers/comment_store_provider.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';

/// A highly optimized and memory-efficient notifier for managing a list of PhotoData.
///
/// ⚠️ Do not modify unless you have a deep understanding of how it works.
final class PhotoStoreNotifier extends Notifier<List<PhotoData>> {
  // Caches photo ID to index for fast lookup
  final Map<int, int> _idIndexCache = {};

  @override
  List<PhotoData> build() {
    // Clear the index cache when the provider is disposed
    ref.onDispose(() {
      _idIndexCache.clear();
    });

    return [];
  }

  void _updateState(List<PhotoData> newState) {
    // Prevent unnecessary state rebuilds by checking if the state actually changed
    if (!listEquals(state, newState)) {
      state = newState;
      _rebuildIndexCache();
    }
  }

  void _rebuildIndexCache() {
    // Rebuilds the internal cache mapping photo IDs to their indices
    _idIndexCache.clear();
    for (var i = 0; i < state.length; i++) {
      _idIndexCache[state[i].id] = i;
    }
  }

  PhotoData? getItem(int id) {
    final index = _idIndexCache[id];
    if (index == null || index >= state.length) return null;
    return state[index];
  }

  List<PhotoData> getItems(List<int> ids) {
    if (ids.isEmpty) return const [];

    // Efficiently collect multiple photo items using the cache
    final result = <PhotoData>[];
    for (final id in ids) {
      final photo = getItem(id);
      if (photo != null) {
        result.add(photo);
      }
    }
    return result;
  }

  int getIndex(int id) {
    return _idIndexCache[id] ?? -1;
  }

  bool hasItem(int id) {
    return _idIndexCache.containsKey(id);
  }

  /// Adds a new photo only if it doesn't already exist.
  ///
  /// The order of photos does not matter in this provider.
  void addItem(PhotoData newItem, {bool updateIfExists = true}) {
    if (hasItem(newItem.id)) {
      if (updateIfExists) updateItem(newItem);
      return;
    }

    _updateState([...state, newItem]);
  }

  /// Adds a list of photos uniquely, optionally updating existing ones.
  ///
  /// Designed for performance with large batch additions.
  void addItems(List<PhotoData> newItems, {bool updateIfExists = true}) {
    if (newItems.isEmpty) return;

    final existingIds = _idIndexCache.keys.toSet();
    final parsedItems = <PhotoData>[...state];

    if (updateIfExists) {
      final existingItemsToUpdate = <PhotoData>[];
      final newItemsToAdd = <PhotoData>[];

      for (final item in newItems) {
        if (existingIds.contains(item.id)) {
          existingItemsToUpdate.add(item);
        } else {
          newItemsToAdd.add(item);
        }
      }

      // Efficiently update existing items
      for (final item in existingItemsToUpdate) {
        final index = _idIndexCache[item.id]!;
        if (parsedItems[index] != item) {
          parsedItems[index] = item;
        }
      }

      // Add new items
      if (newItemsToAdd.isNotEmpty) {
        parsedItems.addAll(newItemsToAdd);
      }
    } else {
      // Only add items not already in the store
      final newItemsToAdd = newItems
          .where((item) => !existingIds.contains(item.id))
          .toList();
      if (newItemsToAdd.isNotEmpty) {
        parsedItems.addAll(newItemsToAdd);
      }
    }

    _updateState(parsedItems);
  }

  /// Updates a single existing item.
  ///
  /// If the item doesn't exist, it can optionally be added.
  void updateItem(PhotoData newItem, {bool addIfNotExists = true}) {
    final index = _idIndexCache[newItem.id];

    if (index == null) {
      if (addIfNotExists) {
        _updateState([...state, newItem]);
      }
      return;
    }

    if (state[index] == newItem) return;

    final updated = <PhotoData>[...state];
    updated[index] = newItem;
    _updateState(updated);
  }

  /// Efficiently updates a batch of existing items.
  ///
  /// Can optionally add non-existing items.
  void updateItems(List<PhotoData> newItems, {bool addIfNotExists = true}) {
    if (newItems.isEmpty) return;

    final existingIds = _idIndexCache.keys.toSet();
    final parsedItems = <PhotoData>[...state];
    bool hasChanges = false;

    final existingItemsToUpdate = <PhotoData>[];
    final newItemsToAdd = <PhotoData>[];

    for (final item in newItems) {
      if (existingIds.contains(item.id)) {
        existingItemsToUpdate.add(item);
      } else if (addIfNotExists) {
        newItemsToAdd.add(item);
      }
    }

    for (final item in existingItemsToUpdate) {
      final index = _idIndexCache[item.id]!;
      if (parsedItems[index] != item) {
        parsedItems[index] = item;
        hasChanges = true;
      }
    }

    if (newItemsToAdd.isNotEmpty) {
      parsedItems.addAll(newItemsToAdd);
      hasChanges = true;
    }

    if (hasChanges) {
      _updateState(parsedItems);
    }
  }

  /// Removes a photo by ID and cleans up any related data.
  void removeItem(int id) {
    if (!_idIndexCache.containsKey(id)) return;

    final updated = state
        .where((item) => item.id != id)
        .toList(growable: false);
    _updateState(updated);

    try {
      ref.read(commentStoreProvider.notifier).removeByPhotoId(id);
      ref.read(commentListManagerProvider.notifier).clearByPhotoId(id);
    } catch (error) {
      if (kDebugMode) {
        debugPrint('Error cleaning up related data for photo $id: $error');
      }
    }
  }

  /// Removes multiple photos by their IDs and cleans related data.
  void removeItems(List<int> ids) {
    if (ids.isEmpty) return;

    final idsToRemove = ids.toSet();
    final updated = state
        .where((item) => !idsToRemove.contains(item.id))
        .toList(growable: false);

    if (updated.length != state.length) {
      _updateState(updated);

      try {
        final commentStore = ref.read(commentStoreProvider.notifier);
        final commentListManager = ref.read(
          commentListManagerProvider.notifier,
        );

        for (final id in ids) {
          commentStore.removeByPhotoId(id);
          commentListManager.clearByPhotoId(id);
        }
      } catch (error) {
        if (kDebugMode) {
          debugPrint('Error cleaning up related data for photos $ids: $error');
        }
      }
    }
  }

  /// Removes all photos created by a specific author.
  void removeByAuthorId(int authorId) {
    final updated = state
        .where((item) => item.authorId != authorId)
        .toList(growable: false);

    if (updated.length != state.length) {
      _updateState(updated);

      try {
        ref.read(commentStoreProvider.notifier).removeByAuthorId(authorId);
      } catch (error) {
        if (kDebugMode) {
          debugPrint('Error cleaning up comments for author $authorId: $error');
        }
      }
    }
  }

  /// Replaces the entire store with a new list of photos.
  void replaceAll(List<PhotoData> newList) {
    _updateState(List.from(newList, growable: false));
  }

  /// Clears all photos in the store.
  void clear() {
    if (state.isNotEmpty) {
      _updateState(const []);
    }
  }

  // ------------------------------------------------------------
  // Specialized update methods for single photo fields
  // Designed to avoid unnecessary state updates
  // ------------------------------------------------------------

  void _updatePhotoField(int photoId, PhotoData Function(PhotoData) updater) {
    final index = _idIndexCache[photoId];
    if (index == null || index >= state.length) return;

    final currentPhoto = state[index];
    final updatedPhoto = updater(currentPhoto);

    if (currentPhoto != updatedPhoto) {
      final updated = <PhotoData>[...state];
      updated[index] = updatedPhoto;
      _updateState(updated);
    }
  }

  void setPotd(int photoId, bool isPotd) {
    _updatePhotoField(photoId, (photo) => photo.copyWith(potd: isPotd));
  }

  void setFeatured(int photoId, bool isFeatured) {
    _updatePhotoField(photoId, (photo) => photo.copyWith(featured: isFeatured));
  }

  void setTotalLikes(int photoId, int newTotalLikes) {
    _updatePhotoField(
      photoId,
      (photo) => photo.copyWith(totalLikes: newTotalLikes),
    );
  }

  void setIsLiked(int photoId, bool isLiked) {
    _updatePhotoField(photoId, (photo) => photo.copyWith(isLiked: isLiked));
  }

  void setTotalComments(int photoId, int newTotalComments) {
    _updatePhotoField(
      photoId,
      (photo) => photo.copyWith(totalComments: newTotalComments),
    );
  }

  void addTotalComments(int photoId, int amount) {
    if (amount == 0) return;

    _updatePhotoField(
      photoId,
      (photo) => photo.copyWith(totalComments: photo.totalComments + amount),
    );
  }

  void incrementTotalComments(int photoId) {
    _updatePhotoField(
      photoId,
      (photo) => photo.copyWith(totalComments: photo.totalComments + 1),
    );
  }

  void decrementTotalComments(int photoId) {
    _updatePhotoField(
      photoId,
      (photo) => photo.copyWith(
        totalComments: (photo.totalComments - 1)
            .clamp(0, double.infinity)
            .toInt(),
      ),
    );
  }

  // ------------------------------------------------------------
  // Utility getters for common use cases
  // ------------------------------------------------------------

  /// Returns total number of photos
  int get count => state.length;

  /// Checks if the store is empty
  bool get isEmpty => state.isEmpty;

  /// Checks if the store has at least one photo
  bool get isNotEmpty => state.isNotEmpty;

  /// Returns all photo IDs
  List<int> get allIds => _idIndexCache.keys.toList(growable: false);

  /// Returns all photos by a specific author
  List<PhotoData> getPhotosByAuthor(int authorId) {
    return state
        .where((photo) => photo.authorId == authorId)
        .toList(growable: false);
  }

  /// Returns all photos by multiple authors
  List<PhotoData> getPhotosByAuthors(Set<int> authorIds) {
    if (authorIds.isEmpty) return const [];
    return state
        .where((photo) => authorIds.contains(photo.authorId))
        .toList(growable: false);
  }
}

/// NotifierProvider for the main photo store
final photoStoreProvider =
    NotifierProvider.autoDispose<PhotoStoreNotifier, List<PhotoData>>(
      PhotoStoreNotifier.new,
    );

/// A selective provider that only rebuilds when a specific photo field changes
final photoProvider =
    Provider.family<
      ({
        bool? potd,
        bool? featured,
        int? totalLikes,
        int? totalComments,
        bool? isLiked,
      }),
      int
    >((ref, photoId) {
      final store = ref.watch(photoStoreProvider.notifier);
      final photo = store.getItem(photoId);

      return (
        potd: photo?.potd,
        featured: photo?.featured,
        totalLikes: photo?.totalLikes,
        totalComments: photo?.totalComments,
        isLiked: photo?.isLiked,
      );
    });

/// Provider that returns the current total photo count
final photoCountProvider = Provider.autoDispose<int>((ref) {
  return ref.watch(photoStoreProvider.select((photos) => photos.length));
});

/// Provider that checks if the store has any photos
final hasPhotosProvider = Provider.autoDispose<bool>((ref) {
  return ref.watch(photoStoreProvider.select((photos) => photos.isNotEmpty));
});

/// Provider that returns all photos by a given author ID
final photosByAuthorProvider = Provider.family
    .autoDispose<List<PhotoData>, int>((ref, authorId) {
      return ref.watch(
        photoStoreProvider.select(
          (photos) => photos
              .where((photo) => photo.authorId == authorId)
              .toList(growable: false),
        ),
      );
    });

/// Provider that checks if a specific photo ID exists
final hasPhotoProvider = Provider.family.autoDispose<bool, int>((ref, photoId) {
  return ref.watch(photoStoreProvider.notifier).hasItem(photoId);
});

/// Provider that returns a specific photo by ID
final specificPhotoProvider = Provider.family.autoDispose<PhotoData?, int>((
  ref,
  photoId,
) {
  return ref.watch(photoStoreProvider.notifier).getItem(photoId);
});
