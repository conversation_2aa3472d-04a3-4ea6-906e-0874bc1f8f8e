import 'dart:async';
import 'package:flutter/material.dart';

typedef FutureCallBack = Future<bool> Function();

enum SliverGridLoadMoreStatusState { idle, loading, failed, finished }

class _BuildNotification extends Notification {}

class _RetryNotification extends Notification {}

class SliverGridLoadMoreStatusText {
  static const String idle = 'Scroll to load more';
  static const String loading = 'Loading...';
  static const String failed = 'Failed to load items';
  static const String finished = 'No more items';

  static String getText(SliverGridLoadMoreStatusState state) {
    switch (state) {
      case SliverGridLoadMoreStatusState.loading:
        return loading;
      case SliverGridLoadMoreStatusState.failed:
        return failed;
      case SliverGridLoadMoreStatusState.finished:
        return finished;
      default:
        return idle;
    }
  }
}

class SliverGridLoadMoreLoadingWidgetDefaultOpts {
  static const double containerHeight = 60.0;
  static const double size = 24.0;
  static const double strokeWidth = 3.0;
  static const Color color = Colors.blue;
  static const int delay = 16;
}

class SliverGridLoadMore extends StatefulWidget {
  /// The height of the loading widget's container/wrapper.
  final double loadingWidgetContainerHeight;

  /// The loading widget size.
  final double loadingWidgetSize;

  /// The loading widget stroke width.
  final double loadingWidgetStrokeWidth;

  /// The loading widget color.
  final Color loadingWidgetColor;

  /// The loading widget animation delay.
  final int loadingWidgetAnimationDelay;

  /// Status text to show when the load more is not triggered.
  final String idleStatusText;

  /// Status text to show when the process is loading.
  final String loadingStatusText;

  /// Status text to show when the processing is failed.
  final String failedStatusText;

  /// Status text to show when there's no more items to load.
  final String finishedStatusText;

  /// Manually turn-off the next load more.
  ///
  /// Set this to `true` to set the load more as `finished` (no more items). Default is `false`.
  ///
  /// The use-case is when there's no more items to load, you might want `SliverGridLoadMore` to not running again.
  final bool isFinished;

  /// Whether or not to run the load more even though the result is empty/finished.
  final bool runOnEmptyResult;

  /// Callback function to run during the load more process.
  ///
  /// To mark the status as success or delay, set the return to `true`.
  ///
  /// To mark the status as failed, set the return to `false`.
  final FutureCallBack onLoadMore;

  final double verticalPaddingValue;
  final double horizontalPaddingValue;
  final int crossAxisCount;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final List<Widget> children;

  const SliverGridLoadMore({
    super.key,
    this.loadingWidgetContainerHeight =
        SliverGridLoadMoreLoadingWidgetDefaultOpts.containerHeight,
    this.loadingWidgetSize = SliverGridLoadMoreLoadingWidgetDefaultOpts.size,
    this.loadingWidgetStrokeWidth =
        SliverGridLoadMoreLoadingWidgetDefaultOpts.strokeWidth,
    this.loadingWidgetColor = SliverGridLoadMoreLoadingWidgetDefaultOpts.color,
    this.loadingWidgetAnimationDelay =
        SliverGridLoadMoreLoadingWidgetDefaultOpts.delay,
    this.idleStatusText = SliverGridLoadMoreStatusText.idle,
    this.loadingStatusText = SliverGridLoadMoreStatusText.loading,
    this.failedStatusText = SliverGridLoadMoreStatusText.failed,
    this.finishedStatusText = SliverGridLoadMoreStatusText.finished,
    this.isFinished = false,
    this.runOnEmptyResult = false,
    required this.onLoadMore,
    this.verticalPaddingValue = 0.0,
    this.horizontalPaddingValue = 0.0,
    required this.crossAxisCount,
    required this.mainAxisSpacing,
    required this.crossAxisSpacing,
    required this.children,
  });

  @override
  SliverGridLoadMoreState createState() => SliverGridLoadMoreState();
}

class SliverGridLoadMoreState extends State<SliverGridLoadMore> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: widget.horizontalPaddingValue),
      child: CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
            child: SizedBox(height: widget.verticalPaddingValue),
          ),
          if (widget.children.isNotEmpty) _buildSliverGrid(),
          _buildLoadMoreView(notify: (widget.children.isEmpty ? true : false)),
        ],
      ),
    );
  }

  Widget _buildSliverGrid() {
    List<Widget> children = widget.children;

    if (children.isNotEmpty) {
      // Modify the last item to include the load more widget.
      children[children.length - 1] = Stack(
        children: [
          children[children.length - 1],
          _buildLoadMoreView(notify: true, visual: false),
        ],
      );
    }

    return SliverGrid.count(
      crossAxisCount: widget.crossAxisCount,
      mainAxisSpacing: widget.mainAxisSpacing,
      crossAxisSpacing: widget.crossAxisSpacing,
      childAspectRatio: 1.8,
      children: children,
    );
  }

  SliverGridLoadMoreStatusState status = SliverGridLoadMoreStatusState.idle;

  Widget _buildLoadMoreView({bool notify = false, bool visual = true}) {
    if (widget.isFinished == true) {
      status = SliverGridLoadMoreStatusState.finished;
    } else {
      if (status == SliverGridLoadMoreStatusState.finished) {
        status = SliverGridLoadMoreStatusState.idle;
      }
    }

    return NotificationListener<_RetryNotification>(
      onNotification: _onRetry,
      child: NotificationListener<_BuildNotification>(
        onNotification: _onLoadMoreBuild,
        child: SliverGridLoadMoreView(
          status: status,
          notify: notify,
          visual: visual,
          containerHeight: widget.loadingWidgetContainerHeight,
          size: widget.loadingWidgetSize,
          strokeWidth: widget.loadingWidgetStrokeWidth,
          color: widget.loadingWidgetColor,
          animationDelay: widget.loadingWidgetAnimationDelay,
          idleStatusText: widget.idleStatusText,
          loadingStatusText: widget.loadingStatusText,
          failedStatusText: widget.failedStatusText,
          finishedStatusText: widget.finishedStatusText,
        ),
      ),
    );
  }

  bool _onLoadMoreBuild(_BuildNotification notification) {
    if (status == SliverGridLoadMoreStatusState.idle) {
      loadMore();
    }

    if (status == SliverGridLoadMoreStatusState.loading) {
      return false;
    }

    if (status == SliverGridLoadMoreStatusState.failed) {
      return false;
    }

    if (status == SliverGridLoadMoreStatusState.finished) {
      return false;
    }

    return false;
  }

  void _updateStatus(SliverGridLoadMoreStatusState status) {
    if (mounted) setState(() => this.status = status);
  }

  bool _onRetry(_RetryNotification notification) {
    loadMore();
    return false;
  }

  void loadMore() {
    _updateStatus(SliverGridLoadMoreStatusState.loading);

    widget.onLoadMore().then((v) {
      if (v == true) {
        _updateStatus(SliverGridLoadMoreStatusState.idle);
      } else {
        _updateStatus(SliverGridLoadMoreStatusState.failed);
      }
    });
  }
}

class SliverGridLoadMoreView extends StatefulWidget {
  final SliverGridLoadMoreStatusState status;
  final bool notify;
  final bool visual;

  final double containerHeight;
  final double size;
  final double strokeWidth;
  final Color color;
  final int animationDelay;

  final String idleStatusText;
  final String loadingStatusText;
  final String failedStatusText;
  final String finishedStatusText;

  const SliverGridLoadMoreView({
    super.key,
    required this.status,
    this.notify = false,
    this.visual = true,
    required this.containerHeight,
    required this.size,
    required this.strokeWidth,
    required this.color,
    required this.animationDelay,
    required this.idleStatusText,
    required this.loadingStatusText,
    required this.failedStatusText,
    required this.finishedStatusText,
  });

  @override
  State<SliverGridLoadMoreView> createState() => _SliverGridLoadMoreViewState();
}

class _SliverGridLoadMoreViewState extends State<SliverGridLoadMoreView> {
  final buildNotification = _BuildNotification();
  final retryNotification = _RetryNotification();

  @override
  Widget build(BuildContext context) {
    if (widget.notify) {
      notify();
    }

    if (!widget.visual) {
      // If `visual` is false, we don't need to visually show any widget.
      // And it returns RenderBox (regular) widget instead of Sliver widget
      // because it will be inserted in last item of the grids.
      return const SizedBox.shrink();
    }

    // If `visual` is true, we build the actual status indicator.
    // And it returns Sliver widget because it will be displayed
    // in the CustomScrollView's children as last item.
    return SliverToBoxAdapter(
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          if (widget.status == SliverGridLoadMoreStatusState.failed ||
              widget.status == SliverGridLoadMoreStatusState.idle) {
            _notifyRetryProcess();
          }
        },
        child: Container(
          height: widget.containerHeight,
          alignment: Alignment.center,
          child: buildTextWidget(),
        ),
      ),
    );
  }

  Widget buildTextWidget() {
    String text = '';

    switch (widget.status) {
      case SliverGridLoadMoreStatusState.idle:
        text = widget.idleStatusText;
        break;
      case SliverGridLoadMoreStatusState.loading:
        text = widget.loadingStatusText;
        break;
      case SliverGridLoadMoreStatusState.failed:
        text = widget.failedStatusText;
        break;
      case SliverGridLoadMoreStatusState.finished:
        text = widget.finishedStatusText;
        break;
    }

    if (widget.status == SliverGridLoadMoreStatusState.failed) {
      return Container(padding: const EdgeInsets.all(0.0), child: Text(text));
    }

    if (widget.status == SliverGridLoadMoreStatusState.idle) {
      return Text(text);
    }

    if (widget.status == SliverGridLoadMoreStatusState.loading) {
      return Container(
        alignment: Alignment.center,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            SizedBox(
              width: widget.size,
              height: widget.size,
              child: CircularProgressIndicator(
                strokeWidth: widget.strokeWidth,
                valueColor: AlwaysStoppedAnimation<Color>(widget.color),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 10.0),
              child: Text(text),
            ),
          ],
        ),
      );
    }

    if (widget.status == SliverGridLoadMoreStatusState.finished) {
      return Text(text);
    }

    return Text(text);
  }

  void notify() async {
    Duration delay = max(
      Duration(microseconds: widget.animationDelay),
      const Duration(
        milliseconds: SliverGridLoadMoreLoadingWidgetDefaultOpts.delay,
      ),
    );

    await Future.delayed(delay);

    if (widget.status == SliverGridLoadMoreStatusState.idle) {
      _notifyBuildProcess();
    }
  }

  Duration max(Duration duration, Duration duration2) {
    if (duration > duration2) {
      return duration;
    }
    return duration2;
  }

  void _notifyBuildProcess() {
    if (!mounted) return;
    buildNotification.dispatch(context);
  }

  void _notifyRetryProcess() {
    if (!mounted) return;
    retryNotification.dispatch(context);
  }
}
