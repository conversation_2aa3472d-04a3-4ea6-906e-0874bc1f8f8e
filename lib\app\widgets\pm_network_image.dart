import 'package:cached_network_image/cached_network_image.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/utils/cache_util.dart';

enum PmNetworkImageRenderer { networkImage, cachedNetworkImage, extendedImage }

const defaultImageRenderer = PmNetworkImageRenderer.cachedNetworkImage;

class PmNetworkImage extends StatefulWidget {
  final String url;
  final double? width;
  final double? height;
  final Alignment? alignment;
  final BoxFit? fit;
  final Widget? loadingWidget;
  final ImageErrorWidgetBuilder? errorBuilder;
  final PmNetworkImageRenderer renderer;

  const PmNetworkImage({
    super.key,
    required this.url,
    this.width,
    this.height,
    this.alignment,
    this.fit,
    this.loadingWidget,
    this.errorBuilder,
    this.renderer = defaultImageRenderer,
  });

  @override
  PmNetworkImageState createState() => PmNetworkImageState();
}

class PmNetworkImageState extends State<PmNetworkImage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animController;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();

    _animController = AnimationController(
      duration: const Duration(milliseconds: 75),
      vsync: this,
    );

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_animController);

    _animController.forward();
  }

  @override
  void dispose() {
    _animController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.renderer == PmNetworkImageRenderer.cachedNetworkImage) {
      return _buildCachedNetworkImage();
    } else if (widget.renderer == PmNetworkImageRenderer.extendedImage) {
      return _buildExtendedImageNetwork();
    }

    return _buildNetworkImage();
  }

  Image _buildNetworkImage() {
    return Image.network(
      widget.url,
      alignment: widget.alignment ?? Alignment.center,
      width: widget.width,
      height: widget.height,
      fit: widget.fit ?? BoxFit.cover,
      opacity: _opacityAnimation,
      loadingBuilder:
          (
            BuildContext context,
            Widget child,
            ImageChunkEvent? loadingProgress,
          ) {
            if (loadingProgress == null) {
              return child;
            }

            return widget.loadingWidget ??
                Container(
                  color: context.colors.lightColor,
                  width: widget.width,
                  height: widget.height,
                );
          },
      errorBuilder:
          widget.errorBuilder ??
          (BuildContext context, Object error, StackTrace? stackTrace) {
            return Container(
              color: context.colors.lightColor,
              width: widget.width,
              height: widget.height,
            );
          },
    );
  }

  CachedNetworkImage _buildCachedNetworkImage() {
    return CachedNetworkImage(
      imageUrl: widget.url,
      alignment: Alignment.center,
      fit: BoxFit.cover,
      width: widget.width,
      height: widget.height,
      cacheManager: PmCacheManager(),
      fadeInDuration: Duration.zero,
      fadeOutDuration: Duration.zero,
      placeholder: (context, url) {
        return widget.loadingWidget ??
            Container(
              width: widget.width,
              height: widget.height,
              color: context.colors.lightColor,
            );
      },
      errorWidget: (BuildContext context, String url, dynamic error) {
        return Container(
          width: widget.width,
          height: widget.height,
          color: context.colors.lightColor,
        );
      },
    );
  }

  ExtendedImage _buildExtendedImageNetwork() {
    return ExtendedImage.network(
      widget.url,
      width: widget.width,
      height: widget.height,
      fit: widget.fit ?? BoxFit.cover,
      alignment: widget.alignment ?? Alignment.center,
      cache: true,
      loadStateChanged: (ExtendedImageState state) {
        switch (state.extendedImageLoadState) {
          case LoadState.loading:
            return widget.loadingWidget ??
                Container(
                  color: context.colors.lightColor,
                  width: widget.width,
                  height: widget.height,
                );
          case LoadState.completed:
            return ExtendedRawImage(
              image: state.extendedImageInfo?.image,
              width: widget.width,
              height: widget.height,
              fit: widget.fit ?? BoxFit.cover,
            );
          case LoadState.failed:
            return Container(
              color: context.colors.lightColor,
              width: widget.width,
              height: widget.height,
            );
        }
      },
    );
  }
}

class PmNetworkImageProvider {
  final String url;
  final PmNetworkImageRenderer renderer;

  PmNetworkImageProvider(this.url, {this.renderer = defaultImageRenderer});

  ImageProvider<Object> get imageProvider {
    switch (renderer) {
      case PmNetworkImageRenderer.cachedNetworkImage:
        return cachedNetworkImage;
      case PmNetworkImageRenderer.extendedImage:
        return extendedImage;
      default:
        return networkImage;
    }
  }

  CachedNetworkImageProvider get cachedNetworkImage =>
      CachedNetworkImageProvider(url);

  ExtendedNetworkImageProvider get extendedImage =>
      ExtendedNetworkImageProvider(url);

  NetworkImage get networkImage => NetworkImage(url);
}
