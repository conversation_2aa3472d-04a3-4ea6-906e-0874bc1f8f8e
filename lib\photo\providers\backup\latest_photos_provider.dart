import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

final class LatestPhotoIdsNotifier extends IdListNotifier {}

final latestPhotoIdsProvider =
    NotifierProvider.autoDispose<LatestPhotoIdsNotifier, List<int>>(
      LatestPhotoIdsNotifier.new,
    );

final latestPhotosProvider = Provider.autoDispose<List<PhotoData>>((ref) {
  final ids = ref.watch(latestPhotoIdsProvider);
  final store = ref.watch(photoStoreProvider);
  return ids.map((id) => store[id]).whereType<PhotoData>().toList();
});
