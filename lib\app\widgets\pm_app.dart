import 'dart:async';
import 'dart:developer';

import 'package:app_links/app_links.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/providers/app_state_provider.dart';
import 'package:portraitmode/app/utils/theme_manager.dart';
import 'package:portraitmode/app/utils/theme_util.dart';
import 'package:portraitmode/home/<USER>/latest_photos_demo_screen.dart';

class PmApp extends ConsumerStatefulWidget {
  const PmApp({super.key});

  @override
  PmAppState createState() => PmAppState();
}

class PmAppState extends ConsumerState<PmApp> with WidgetsBindingObserver {
  late AppLinks _appLinks;
  StreamSubscription<Uri>? _linkSubscription;

  final Duration _inBgDurationToCheck = const Duration(minutes: 30);
  DateTime? _timeWentToBackground;
  Timer? _backgroundTimer;

  @override
  void initState() {
    super.initState();
    _initDeepLinks();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    _linkSubscription?.cancel();
    _backgroundTimer?.cancel();

    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    bool behaveNewlyOpened = false;

    if (state == AppLifecycleState.paused) {
      _timeWentToBackground = DateTime.now();
      _backgroundTimer = Timer(_inBgDurationToCheck, () {
        log('App is in background for $_inBgDurationToCheck minutes or more.');
      });
    } else if (state == AppLifecycleState.resumed) {
      if (_timeWentToBackground != null) {
        final durationInBackground = DateTime.now().difference(
          _timeWentToBackground!,
        );
        if (durationInBackground >= _inBgDurationToCheck) {
          log(
            'App is resumed from background after $_timeWentToBackground minutes or more.',
          );
          behaveNewlyOpened = true;
        }
      }

      _backgroundTimer?.cancel();
    }

    ref.read(appStateProvider.notifier).setBehaveNewlyOpened(behaveNewlyOpened);
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: ThemeManager.instance,
      builder: (context, themeMode, child) {
        return MaterialApp(
          title: 'PortraitMode',
          debugShowCheckedModeBanner: false,
          themeMode: ThemeManager.instance.materialThemeMode,
          theme: getThemeData(context, ThemeMode.light),
          darkTheme: getThemeData(context, ThemeMode.dark),
          // home: const ScreenSelector(),
          home: const LatestPhotosDemoScreen(),
          builder: (context, child) =>
              _SystemUIWrapper(child: child ?? const SizedBox.shrink()),
        );
      },
    );
  }

  Future<void> _initDeepLinks() async {
    _appLinks = AppLinks();

    // Check initial link if app was in cold state (terminated)
    final appLink = await _appLinks.getInitialLink();

    if (appLink != null) {
      log('getInitialAppLink: $appLink');
      _openAppLink(appLink);
    }

    // Handle link when app is in warm state (front or background)
    _linkSubscription = _appLinks.uriLinkStream.listen((uri) {
      log('onAppLink: $uri');
      _openAppLink(uri);
    });
  }

  /// If the link contains https://portraitmode.io/profile/usernicename
  /// then open the profile screen.
  ///
  /// If the link contains https://portraitmode.io/photo-somerandomslug
  /// then open the photo detail screen.
  ///
  /// Otherwise, open the home screen.
  void _openAppLink(Uri uri) {
    final String path = uri.path;
    final String host = uri.host;
    final String query = uri.query;

    log('_openAppLink with path = "$path", host = "$host", query = "$query"');
    if (host != 'portraitmode.io') return;
  }
}

// Separate widget for system UI updates to avoid rebuilding everything
class _SystemUIWrapper extends StatelessWidget {
  final Widget child;

  const _SystemUIWrapper({required this.child});

  @override
  Widget build(BuildContext context) {
    // This rebuilds when MaterialApp's theme changes, not when our ThemeManager changes
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarBrightness: context.isDarkMode
            ? Brightness.dark
            : Brightness.light,
        statusBarIconBrightness: context.isDarkMode
            ? Brightness.light
            : Brightness.dark,
        systemNavigationBarColor: context.colors.scaffoldColor,
        systemNavigationBarIconBrightness: context.isDarkMode
            ? Brightness.light
            : Brightness.dark,
      ),
    );

    return child;
  }
}
