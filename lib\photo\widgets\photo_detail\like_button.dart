import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/photo/http_responses/like_photo_response.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/like_photo_service.dart';

class LikeButton extends ConsumerStatefulWidget {
  final int photoId;
  final bool isLiked;
  final int totalLikes;

  const LikeButton({
    super.key,
    required this.photoId,
    this.isLiked = false,
    this.totalLikes = 0,
  });

  @override
  LikeButtonState createState() => LikeButtonState();
}

class LikeButtonState extends ConsumerState<LikeButton> {
  final likePhotoService = LikePhotoService();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final bool isLiked =
        ref.watch(photoProvider(widget.photoId)).isLiked ?? widget.isLiked;

    // log('    build LikeButton\n      photo id: ${widget.photoId.toString()}\n      isLiked: ${isLiked.toString()}');

    return Row(
      children: [
        (isLiked ? likedButton() : likeButton()),
        const SizedBox(width: 3.0),
      ],
    );
  }

  Widget likeButton() {
    Color? textColor = Theme.of(context).textTheme.bodySmall?.color;

    return OutlinedButton.icon(
      onPressed: () {
        doAction('like');
      },
      label: Text(
        'Like',
        style: TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: 12.0,
          color: textColor,
        ),
      ),
      icon: Icon(Ionicons.heart_outline, size: 14.0, color: textColor),
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12.0),
      ),
    );
  }

  Widget likedButton() {
    return ElevatedButton(
      onPressed: () async {
        doAction('unlike');
      },
      style: OutlinedButton.styleFrom(
        elevation: 0.0,
        backgroundColor: context.colors.accentColor,
        padding: const EdgeInsets.symmetric(horizontal: 12.0),
      ),
      child: const Row(
        children: [
          Icon(Ionicons.heart, size: 14.0, color: Colors.white),
          SizedBox(width: 7.0),
          Text(
            'Liked',
            style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 12.0,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void doAction(String actionType) async {
    // Switch the isLiked state even though the request hasn't been made.
    ref
        .read(photoStoreProvider.notifier)
        .setIsLiked(widget.photoId, actionType == 'like');

    LikePhotoResponse response = actionType == 'like'
        ? await likePhotoService.like(widget.photoId)
        : await likePhotoService.unlike(widget.photoId);

    if (!response.success || response.data == null) {
      // Switch the isLiked state back to the previous state.
      ref
          .read(photoStoreProvider.notifier)
          .setIsLiked(widget.photoId, actionType != 'like');

      return;
    }

    ref
        .read(photoStoreProvider.notifier)
        .setTotalLikes(widget.photoId, response.data!.totalLikes);
  }
}
