import 'dart:developer';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

final class FeaturedPhotoIdsNotifier extends IdListNotifier {}

final featuredPhotoIdsProvider =
    NotifierProvider.autoDispose<FeaturedPhotoIdsNotifier, List<int>>(
      FeaturedPhotoIdsNotifier.new,
    );

final featuredPhotosProvider = Provider.autoDispose<List<PhotoData>>((ref) {
  final ids = ref.watch(featuredPhotoIdsProvider);

  log('ids from featuredPhotosProvider: $ids');

  final store = ref.watch(photoStoreProvider);

  log('store from featuredPhotosProvider: $store');

  return ids.map((id) => store[id]).whereType<PhotoData>().toList();
});
