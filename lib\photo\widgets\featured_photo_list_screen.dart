import 'package:easy_load_more/easy_load_more.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/providers/app_state_provider.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/common/utils/common_refresh_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/providers/featured_photos_provider.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/utils/photo_util.dart';
import 'package:portraitmode/photo/widgets/photo_list_item.dart';

class FeaturedPhotoListScreen extends ConsumerStatefulWidget {
  const FeaturedPhotoListScreen({super.key, this.onInit});

  final Function(Function)? onInit;

  @override
  FeaturedPhotoListScreenState createState() => FeaturedPhotoListScreenState();
}

class FeaturedPhotoListScreenState
    extends ConsumerState<FeaturedPhotoListScreen> {
  final _scrollController = ScrollController();
  bool _blockScrolling = false;
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  late final int _profileId;

  final int _loadMorePerPage = LoadMoreConfig.photosPerPage;
  int _loadMoreLastId = 0;
  bool _loadMoreEndReached = false;

  final _photoListService = PhotoListService();

  @override
  void initState() {
    widget.onInit?.call(_scrollToTop);
    _profileId = LocalUserService.userId ?? 0;
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // log('build screen: FeaturedPhotoListScreen');

    return Consumer(
      builder: (context, ref, child) {
        bool behaveNewlyOpened = ref.watch(
          appStateProvider.select((data) => data.behaveNewlyOpened),
        );

        if (behaveNewlyOpened) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _scrollToTop();

            if (mounted) {
              // Reset the behaveNewlyOpened flag after handling it.
              ref.read(appStateProvider.notifier).setBehaveNewlyOpened(false);
            }
          });
        }

        return _buildScreen();
      },
    );
  }

  Widget _buildScreen() {
    List<PhotoData> photoList = ref.watch(featuredPhotosProvider);

    return Scaffold(
      body: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 768.0),
          child: RefreshIndicator(
            key: _refreshIndicatorKey,
            onRefresh: _handleRefresh,
            child: CustomScrollView(
              controller: _scrollController,
              cacheExtent: 5000,
              physics: _blockScrolling
                  ? const NeverScrollableScrollPhysics()
                  : null,
              slivers: <Widget>[
                PmSliverAppBar(scrollController: _scrollController),
                EasyLoadMore(
                  isFinished: _loadMoreEndReached,
                  onLoadMore: _handleLoadMore,
                  loadingWidgetColor: context.colors.baseColorAlt,
                  runOnEmptyResult: true,
                  idleStatusText: "",
                  loadingStatusText: "",
                  finishedStatusText: "",
                  child: _buildSliverListView(photoList),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSliverListView(List<PhotoData> photoList) {
    return SliverList(
      delegate: SliverChildBuilderDelegate((BuildContext context, int index) {
        double marginTop = index == 0 ? LayoutConfig.contentTopGap : 12.0;
        bool isOwnProfile = photoList[index].authorId == _profileId;

        return Container(
          margin: EdgeInsets.only(top: marginTop),
          child: PhotoListItem(
            index: index,
            photo: photoList[index],
            isOwnProfile: isOwnProfile,
            screenName: 'featured_screen',
            onTwoFingersOn: () {
              if (!mounted) return;

              setState(() {
                _blockScrolling = true;
              });
            },
            onTwoFingersOff: () {
              if (!mounted) return;

              setState(() {
                _blockScrolling = false;
              });
            },
          ),
        );
      }, childCount: photoList.length),
    );
  }

  void _scrollToTop() async {
    await _scrollController.animateTo(
      -100.0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );

    if (_refreshIndicatorKey.currentState != null) {
      await _refreshIndicatorKey.currentState?.show();
    }
  }

  Future<void> _handleRefresh() async {
    _loadMoreLastId = 0;
    _loadMoreEndReached = false;

    List<dynamic> reponses = await Future.wait([
      _photoListService.fetch(
        limit: _loadMorePerPage,
        lastId: _loadMoreLastId,
        featured: true,
      ),
      CommonRefreshUtil().fetch(ref, _profileId),
    ]);

    final PhotoListResponse photoListResponse = reponses[0];

    _handlePhotoListResponse(photoListResponse, true, false);
  }

  Future<bool> _handleLoadMore() async {
    PhotoListResponse response = await _photoListService.fetch(
      limit: _loadMorePerPage,
      lastId: _loadMoreLastId,
      featured: true,
    );

    final isFirstLoad = _loadMoreLastId == 0;

    _handlePhotoListResponse(response, false, isFirstLoad);

    return response.success;
  }

  void _handlePhotoListResponse(
    PhotoListResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    if (response.data.isEmpty) {
      if (isRefresh) {
        ref.read(featuredPhotoIdsProvider.notifier).replaceAll([]);
      }

      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }

      return;
    }

    _loadMoreLastId = response.data.last.id;

    ref
        .read(photoStoreProvider.notifier)
        .updateItems(response.data, addIfNotExists: true);

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh) {
      ref
          .read(featuredPhotoIdsProvider.notifier)
          .replaceAll(photoListToIdList(response.data));

      if (mounted) {
        setState(() {
          _blockScrolling = false;
        });
      }

      return;
    }

    if (isFirstLoad) {
      ref
          .read(featuredPhotoIdsProvider.notifier)
          .replaceAll(photoListToIdList(response.data));
      return;
    }

    ref
        .read(featuredPhotoIdsProvider.notifier)
        .addItems(photoListToIdList(response.data));
  }
}
