// Core packages.
import 'package:flutter/material.dart';
// Extension packages.
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:numeral/numeral.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';

class FollowingNumber extends ConsumerStatefulWidget {
  const FollowingNumber({
    super.key,
    required this.artistId,
    this.totalFollowing = 0,
    this.numberColor,
    this.textColor,
  });

  final int artistId;
  final int totalFollowing;
  final Color? numberColor;
  final Color? textColor;

  @override
  FollowingNumberState createState() => FollowingNumberState();
}

class FollowingNumberState extends ConsumerState<FollowingNumber> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // log('Building follow icon button of photo id: ${widget.artistId}');

    int totalFollowing = ref
        .watch(artistProvider(widget.artistId))
        .totalFollowing;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Numeral(totalFollowing).beautiful,
          style: TextStyle(
            color: widget.numberColor,
            fontSize: 22,
            fontWeight: FontWeight.w600,
          ),
        ),
        Text(
          'Following',
          style: TextStyle(color: widget.textColor, fontSize: 12.0),
        ),
      ],
    );
  }
}
