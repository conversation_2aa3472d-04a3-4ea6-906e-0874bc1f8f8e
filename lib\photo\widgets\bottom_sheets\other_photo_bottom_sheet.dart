import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/common/enum.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/modals/modal_list_tile.dart';
import 'package:portraitmode/moderation/widgets/modals/delete_reported_photo_modal.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/photo_service.dart';
import 'package:portraitmode/photo/widgets/bottom_sheets/report_photo_bottom_sheet.dart';

class OtherPhotoBottomSheet extends ConsumerStatefulWidget {
  final PhotoData photo;
  final bool isOwnPhoto;
  final String screenName;
  final Function(PhotoData)? onDeletePhotoByMod;
  final Function(int)? onPhotoDeleted;
  final void Function({
    required AssignmentActionType action,
    bool notifyAuthor,
  })?
  onFeaturedAssignment;
  final void Function({required AssignmentActionType action})? onPotdAssignment;

  const OtherPhotoBottomSheet({
    super.key,
    required this.photo,
    this.isOwnPhoto = false,
    required this.screenName,
    this.onDeletePhotoByMod,
    this.onPhotoDeleted,
    this.onFeaturedAssignment,
    this.onPotdAssignment,
  });

  @override
  OtherPhotoBottomSheetState createState() => OtherPhotoBottomSheetState();
}

class OtherPhotoBottomSheetState extends ConsumerState<OtherPhotoBottomSheet> {
  final _photoService = PhotoService();
  bool _isSuggesting = false;
  late final String _role;

  @override
  void initState() {
    super.initState();
    _role = LocalUserService.role ?? 'subscriber';
  }

  @override
  Widget build(BuildContext context) {
    final bool isFeatured =
        ref.watch(photoProvider(widget.photo.id)).featured ??
        widget.photo.featured;

    final bool isPotd =
        ref.watch(photoProvider(widget.photo.id)).potd ?? widget.photo.potd;

    List<Widget> menuItems = [];

    if (_role == 'administrator' || _role == 'editor') {
      menuItems.addAll([
        ModalListTile(
          title: "Delete and notify the author",
          textColor: context.colors.dangerColor,
          iconColor: context.colors.dangerColor,
          iconData: Ionicons.trash_outline,
          onTap: () {
            Navigator.pop(context);
            _showDeletePhotoByModModal();
          },
        ),
        ModalListTile(
          title: "Delete without notification",
          textColor: context.colors.dangerColor,
          iconColor: context.colors.dangerColor,
          iconData: Ionicons.trash_bin_outline,
          onTap: () {
            Navigator.pop(context);
            _showDeletePhotoByModDialog();
          },
        ),
      ]);

      if (isFeatured) {
        menuItems.add(
          ModalListTile(
            title: "Remove from featured",
            iconData: Ionicons.trophy_outline,
            onTap: () {
              Navigator.pop(context);
              widget.onFeaturedAssignment!(
                action: AssignmentActionType.unassign,
              );
            },
          ),
        );

        if (widget.screenName == 'featured_screen') {
          if (isPotd) {
            menuItems.add(
              ModalListTile(
                title: "Unset from potd",
                iconData: Ionicons.trophy_outline,
                onTap: () {
                  Navigator.pop(context);
                  widget.onPotdAssignment?.call(
                    action: AssignmentActionType.unassign,
                  );
                },
              ),
            );
          } else {
            menuItems.add(
              ModalListTile(
                title: "Set as potd",
                textColor: context.colors.successColor,
                iconColor: context.colors.successColor,
                iconData: Ionicons.trophy_outline,
                onTap: () {
                  Navigator.pop(context);
                  widget.onPotdAssignment?.call(
                    action: AssignmentActionType.assign,
                  );
                },
              ),
            );
          }
        }
      } else {
        menuItems.addAll([
          ModalListTile(
            title: "Set as featured and notify the author",
            textColor: context.colors.successColor,
            iconColor: context.colors.successColor,
            iconData: Ionicons.trophy_outline,
            onTap: () {
              Navigator.pop(context);
              widget.onFeaturedAssignment?.call(
                action: AssignmentActionType.assign,
                notifyAuthor: true,
              );
            },
          ),
          ModalListTile(
            title: "Set as featured without notification",
            iconData: Ionicons.trophy_outline,
            onTap: () {
              Navigator.pop(context);
              widget.onFeaturedAssignment?.call(
                action: AssignmentActionType.assign,
                notifyAuthor: false,
              );
            },
          ),
        ]);
      }
    } else {
      menuItems.add(
        ModalListTile(
          title: "Report this photo",
          textColor: context.colors.dangerColor,
          iconColor: context.colors.dangerColor,
          iconData: Ionicons.notifications_outline,
          onTap: () {
            Navigator.pop(context);
            _showReportPhotoModal();
          },
        ),
      );

      if (_role == 'contributor') {
        // Prepend "Suggest to be featured" item to menuItems.
        menuItems.insert(
          0,
          ModalListTile(
            title: "Suggest to be featured",
            textColor: context.colors.successColor,
            iconColor: context.colors.successColor,
            iconData: Ionicons.bookmark_outline,
            isLoading: _isSuggesting,
            onTap: () {
              _handleSuggestTobeFeatured();
            },
          ),
        );
      }
    }

    return Container(
      padding: const EdgeInsets.only(top: 9.0),
      decoration: BoxDecoration(
        color: context.colors.scaffoldColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20.0),
          topRight: Radius.circular(20.0),
        ),
      ),
      child: SafeArea(
        child: SizedBox(
          height:
              BottomSheetConfig.extraSpace +
              (BottomSheetConfig.menuItemHeight * menuItems.length),
          child: Column(children: menuItems),
        ),
      ),
    );
  }

  void _handleSuggestTobeFeatured() async {
    if (_isSuggesting) return;

    setState(() {
      _isSuggesting = true;
    });

    final response = await _photoService.suggestTobeFeatured(
      photoId: widget.photo.id,
    );

    setState(() {
      _isSuggesting = false;
    });

    if (!response.success) {
      if (mounted) {
        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }
      }

      return;
    }

    if (mounted) {
      Navigator.of(context).pop();

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(response.message)));
    }
  }

  void _showReportPhotoModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: context.colors.scaffoldColor,
      builder: (BuildContext context) {
        return DraggableScrollableSheet(
          maxChildSize: 0.9,
          initialChildSize: 0.45,
          expand: false,
          builder: ((context, scrollController) {
            return Container(
              padding: EdgeInsets.only(
                top: 8.0,
                bottom: MediaQuery.viewInsetsOf(context).bottom + 15.0,
              ),
              decoration: BoxDecoration(
                color: context.colors.scaffoldColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20.0),
                  topRight: Radius.circular(20.0),
                ),
              ),
              child: ReportPhotoBottomSheet(
                scrollController: scrollController,
                photoId: widget.photo.id,
              ),
            );
          }),
        );
      },
    );
  }

  void _showDeletePhotoByModDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('Delete photo'),
        content: const Text(
          'Are you sure you want to delete this photo without notifying the author?',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              widget.onDeletePhotoByMod?.call(widget.photo);
            },
            child: Text(
              'Delete',
              style: TextStyle(color: context.colors.dangerColor),
            ),
          ),
        ],
      ),
    );
  }

  void _showDeletePhotoByModModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return DraggableScrollableSheet(
          maxChildSize: 0.9,
          initialChildSize: 0.45,
          expand: false,
          builder: ((context, scrollController) {
            return Container(
              padding: EdgeInsets.only(
                top: 8.0,
                bottom: MediaQuery.viewInsetsOf(context).bottom + 15.0,
              ),
              decoration: BoxDecoration(
                color: context.colors.scaffoldColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20.0),
                  topRight: Radius.circular(20.0),
                ),
              ),
              child: DeleteReportedPhotoModal(
                scrollController: scrollController,
                photo: widget.photo,
                isOwnPhoto: widget.isOwnPhoto,
                onPhotoDeleted: widget.onPhotoDeleted,
              ),
            );
          }),
        );
      },
    );
  }
}
