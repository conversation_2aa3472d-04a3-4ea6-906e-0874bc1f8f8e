import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/avatar.dart';
import 'package:portraitmode/artist/dto/artist_partial_data.dart';
import 'package:portraitmode/artist/widgets/artist_detail_screen.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/base/base_response.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/moderation/dto/curator_data.dart';
import 'package:portraitmode/moderation/dto/reported_photo_reporter_data.dart';
import 'package:portraitmode/moderation/services/moderation_service.dart';
import 'package:portraitmode/moderation/utils/reason.dart';
import 'package:portraitmode/moderation/widgets/modals/review_featured_suggestion_modal.dart';
import 'package:portraitmode/moderation/widgets/modals/review_reported_photo_modal.dart';
import 'package:portraitmode/moderation/widgets/review_photo_list_item/review_photo_frame.dart';
import 'package:portraitmode/moderation/widgets/review_photo_list_item/review_photo_header.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/archived_photos_provider.dart';
import 'package:portraitmode/photo/providers/featured_photos_provider.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/profile/providers/my_album_photo_list_provider.dart';
import 'package:portraitmode/profile/providers/my_album_provider.dart';

class ReviewPhotoListItem extends ConsumerStatefulWidget {
  final int index;
  final double? containerWidth;
  final PhotoData photo;
  final bool isOwnPhoto;
  final String moderationType;
  final List<ReportedPhotoReporterData> reporters;
  final List<CuratorData> curators;
  final Function? onTwoFingersOn;
  final Function? onTwoFingersOff;
  final Function(int)? onModerationDismissed;
  final Function(int)? onPhotoDeleted;
  final Function(int)? onPhotoFeatured;

  const ReviewPhotoListItem({
    super.key,
    required this.index,
    this.containerWidth,
    required this.photo,
    this.isOwnPhoto = false,
    required this.moderationType,
    this.reporters = const [],
    this.curators = const [],
    this.onTwoFingersOn,
    this.onTwoFingersOff,
    this.onModerationDismissed,
    this.onPhotoDeleted,
    this.onPhotoFeatured,
  });

  @override
  ReviewPhotoListItemState createState() => ReviewPhotoListItemState();
}

class ReviewPhotoListItemState extends ConsumerState<ReviewPhotoListItem> {
  final ModerationService _moderationService = ModerationService();
  bool _isProcessing = false;

  @override
  Widget build(BuildContext context) {
    DateTime uploadDate = DateTime.parse(widget.photo.date);
    uploadDate = uploadDate.add(uploadDate.timeZoneOffset).toUtc();

    return Align(
      alignment: Alignment.topCenter,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(
              right: ScreenStyleConfig.horizontalPadding,
              left: ScreenStyleConfig.horizontalPadding,
              bottom: 10.0,
            ),
            child: ReviewPhotoHeader(
              photo: widget.photo,
              isOwnPhoto: widget.isOwnPhoto,
              onDotsMenuTap: _handleDotsMenuTap,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: ReviewPhotoFrame(
              containerWidth: widget.containerWidth,
              photo: widget.photo,
              isOwnPhoto: widget.isOwnPhoto,
              screenName: 'review_reported_photos',
              isProcessing: _isProcessing,
              onTwoFingersOn: widget.onTwoFingersOn,
              onTwoFingersOff: widget.onTwoFingersOff,
              onLongPress: _handlePhotoLongPress,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              right: ScreenStyleConfig.horizontalPadding,
              left: ScreenStyleConfig.horizontalPadding,
              bottom: 8.0,
            ),
            child: widget.moderationType == 'photo_report'
                ? _buildReporterList()
                : widget.moderationType == 'featured_suggestion'
                ? _buildCuratorList()
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  Widget _buildReporterList() {
    if (widget.reporters.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int i = 0; i < widget.reporters.length; i++)
          Padding(
            padding: const EdgeInsets.only(top: 5.0),
            child: Wrap(
              children: [
                Avatar(
                  imageUrl: widget.reporters[i].avatarUrl,
                  size: 14.0,
                  onTap: () => _handleOnCuratorTap(
                    curatorId: widget.reporters[i].id,
                    curatorNicename: widget.reporters[i].nicename,
                    curatorDisplayName: widget.reporters[i].displayName,
                    curatorProfileUrl: widget.reporters[i].profileUrl,
                    curatorAvatarUrl: widget.reporters[i].avatarUrl,
                  ),
                ),
                const SizedBox(width: 5.0),
                GestureDetector(
                  onTap: () => _handleOnCuratorTap(
                    curatorId: widget.reporters[i].id,
                    curatorNicename: widget.reporters[i].nicename,
                    curatorDisplayName: widget.reporters[i].displayName,
                    curatorProfileUrl: widget.reporters[i].profileUrl,
                    curatorAvatarUrl: widget.reporters[i].avatarUrl,
                  ),
                  child: Text(
                    widget.reporters[i].displayName,
                    style: TextStyle(
                      fontSize: 12.0,
                      fontWeight: FontWeight.w600,
                      color: context.colors.timeColor,
                    ),
                  ),
                ),
                if (widget.reporters[i].isCurator)
                  Text(
                    ' (curator)',
                    style: TextStyle(
                      fontSize: 12.0,
                      color: context.colors.brandColorAlt,
                    ),
                  ),
                Text(
                  ' reported this photo with reason ',
                  style: TextStyle(
                    fontSize: 12.0,
                    color: context.colors.timeColor,
                  ),
                ),
                Text(
                  '"${getPhotoReportingReason(widget.reporters[i].reason)}"',
                  style: TextStyle(
                    fontSize: 12.0,
                    color: context.colors.timeColor,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildCuratorList() {
    if (widget.curators.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int i = 0; i < widget.curators.length; i++)
          Padding(
            padding: const EdgeInsets.only(top: 5.0),
            child: Wrap(
              children: [
                Avatar(
                  imageUrl: widget.curators[i].avatarUrl,
                  size: 14.0,
                  onTap: () => _handleOnCuratorTap(
                    curatorId: widget.curators[i].id,
                    curatorNicename: widget.curators[i].nicename,
                    curatorDisplayName: widget.curators[i].displayName,
                    curatorProfileUrl: widget.curators[i].profileUrl,
                    curatorAvatarUrl: widget.curators[i].avatarUrl,
                  ),
                ),
                const SizedBox(width: 5.0),
                GestureDetector(
                  onTap: () => _handleOnCuratorTap(
                    curatorId: widget.curators[i].id,
                    curatorNicename: widget.curators[i].nicename,
                    curatorDisplayName: widget.curators[i].displayName,
                    curatorProfileUrl: widget.curators[i].profileUrl,
                    curatorAvatarUrl: widget.curators[i].avatarUrl,
                  ),
                  child: Text(
                    widget.curators[i].displayName,
                    style: TextStyle(
                      fontSize: 12.0,
                      fontWeight: FontWeight.w600,
                      color: context.colors.timeColor,
                    ),
                  ),
                ),
                Text(
                  ' suggested this photo to be featured',
                  style: TextStyle(
                    fontSize: 12.0,
                    color: context.colors.timeColor,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  void _handleOnCuratorTap({
    int curatorId = 0,
    String curatorNicename = '',
    String curatorDisplayName = '',
    String curatorProfileUrl = '',
    String curatorAvatarUrl = '',
  }) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ArtistDetailScreen(
          isOwnProfile: widget.isOwnPhoto,
          partialData: ArtistPartialData(
            id: curatorId,
            nicename: curatorNicename,
            displayName: curatorDisplayName,
            profileUrl: curatorProfileUrl,
            avatarUrl: curatorAvatarUrl,
          ),
        ),
      ),
    );
  }

  void _handlePhotoLongPress() {
    HapticFeedback.vibrate();

    if (widget.moderationType == 'photo_report') {
      _showReviewReportedPhotoModal();
      return;
    }

    if (widget.moderationType == 'featured_suggestion') {
      _showReviewFeaturedSuggestionModal();
      return;
    }
  }

  void _handleDotsMenuTap() {
    if (widget.moderationType == 'photo_report') {
      _showReviewReportedPhotoModal();
      return;
    }

    if (widget.moderationType == 'featured_suggestion') {
      _showReviewFeaturedSuggestionModal();
      return;
    }
  }

  void _showReviewReportedPhotoModal() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return ReviewReportedPhotoModal(
          photo: widget.photo,
          isOwnPhoto: widget.isOwnPhoto,
          onDeletePhoto: _handleDeletePhoto,
          onModerationDismiss: _handleDismissModeration,
          onPhotoDeleted: widget.onPhotoDeleted,
        );
      },
    );
  }

  void _showReviewFeaturedSuggestionModal() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return ReviewFeaturedSuggestionModal(
          photo: widget.photo,
          isOwnPhoto: widget.isOwnPhoto,
          onModerationDismiss: _handleDismissModeration,
          onPhotoFeatured: _handleFeaturingPhoto,
        );
      },
    );
  }

  void _handleDismissModeration() async {
    _startProcessing();

    BaseResponse response = await _moderationService.dismiss(
      photoId: widget.photo.id,
      moderationType: widget.moderationType,
    );

    _stopProcessing();

    if (!response.success) {
      if (mounted) {
        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }
      }

      return;
    }

    if (widget.onModerationDismissed != null) {
      widget.onModerationDismissed!(widget.photo.id);
    }
  }

  void _handleDeletePhoto() async {
    _startProcessing();

    PhotoData photo = widget.photo;

    BaseResponse response = await _moderationService.deleteReportedPhoto(
      photoId: photo.id,
      reason: 'none',
    );

    _stopProcessing();

    if (!response.success) {
      if (mounted) {
        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }
      }

      return;
    }

    if (widget.isOwnPhoto) {
      ref.read(archivedPhotoIdsProvider.notifier).removeItem(photo.id);
      ref.read(myAlbumProvider.notifier).decrementTotalPhotos(photo.album);

      myAlbumPhotoListProviderMap.forEach((
        String albumSlug,
        NotifierProvider<MyAlbumPhotoListNotifier, List<PhotoData>> provider,
      ) {
        ref.read(provider.notifier).removeItem(photo.id);
      });
    }

    ref.read(photoStoreProvider.notifier).removeItem(photo.id);

    if (widget.onPhotoDeleted != null) {
      widget.onPhotoDeleted!(photo.id);
    }
  }

  Future<void> _handleFeaturingPhoto(int notifyAuthor) async {
    _startProcessing();

    BaseResponse response = await _moderationService.approveFeaturedSuggestion(
      photoId: widget.photo.id,
      notifyAuthor: notifyAuthor,
    );

    _stopProcessing();

    if (!response.success) {
      if (mounted) {
        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }
      }

      return;
    }

    ref
        .read(featuredPhotoIdsProvider.notifier)
        .addItem(widget.photo.id, reorder: true);

    ref.read(photoStoreProvider.notifier).setFeatured(widget.photo.id, true);

    if (widget.onPhotoFeatured != null) {
      widget.onPhotoFeatured!(widget.photo.id);
    }
  }

  void _startProcessing() {
    if (!mounted) return;

    setState(() {
      _isProcessing = true;
    });
  }

  void _stopProcessing() {
    if (!mounted) return;

    setState(() {
      _isProcessing = false;
    });
  }
}
