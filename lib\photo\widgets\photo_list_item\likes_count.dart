import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

class LikesCount extends ConsumerStatefulWidget {
  final int photoId;
  final int totalLikes;
  final EdgeInsetsGeometry padding;

  const LikesCount({
    super.key,
    required this.photoId,
    required this.totalLikes,
    this.padding = const EdgeInsets.only(bottom: 5.0),
  });

  @override
  LikesCountState createState() => LikesCountState();
}

class LikesCountState extends ConsumerState<LikesCount> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    int totalLikes =
        ref.watch(photoProvider(widget.photoId)).totalLikes ??
        widget.totalLikes;

    Color? textColor = Theme.of(context).textTheme.bodySmall?.color;

    return totalLikes > 0
        ? Padding(
            padding: widget.padding,
            child: Row(
              children: [
                Text(
                  totalLikes.toString(),
                  style: TextStyle(
                    fontSize: 12.0,
                    fontWeight: FontWeight.w600,
                    color: textColor,
                  ),
                ),
                const SizedBox(width: 3.0),
                Text(
                  totalLikes > 1 ? 'Likes' : 'Like',
                  style: TextStyle(
                    fontSize: 12.0,
                    fontWeight: FontWeight.w600,
                    color: textColor,
                  ),
                ),
              ],
            ),
          )
        : const SizedBox.shrink();
  }
}
