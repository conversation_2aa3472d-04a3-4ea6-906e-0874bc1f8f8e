import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

typedef FutureCallBack = Future<bool> Function();

enum MasonryLoadMoreStatusState { idle, loading, failed, finished }

class _BuildNotification extends Notification {}

class _RetryNotification extends Notification {}

class MasonryLoadMoreStatusText {
  static const String idle = 'Scroll to load more';
  static const String loading = 'Loading...';
  static const String failed = 'Failed to load items';
  static const String finished = 'No more items';

  static String getText(MasonryLoadMoreStatusState state) {
    switch (state) {
      case MasonryLoadMoreStatusState.loading:
        return loading;
      case MasonryLoadMoreStatusState.failed:
        return failed;
      case MasonryLoadMoreStatusState.finished:
        return finished;
      default:
        return idle;
    }
  }
}

class MasonryLoadMoreLoadingWidgetDefaultOpts {
  static const double containerHeight = 60.0;
  static const double size = 24.0;
  static const double strokeWidth = 3.0;
  static const Color color = Colors.blue;
  static const int delay = 16;
}

class MasonryLoadMore extends StatefulWidget {
  /// The key for `MasonryGridView`.
  final GlobalKey? masonryKey;

  /// The ScrollController for `MasonryGridView`.
  final ScrollController? scrollController;

  /// The height of the loading widget's container/wrapper.
  final double loadingWidgetContainerHeight;

  /// The loading widget size.
  final double loadingWidgetSize;

  /// The loading widget stroke width.
  final double loadingWidgetStrokeWidth;

  /// The loading widget color.
  final Color loadingWidgetColor;

  /// The loading widget animation delay.
  final int loadingWidgetAnimationDelay;

  /// Status text to show when the load more is not triggered.
  final String idleStatusText;

  /// Status text to show when the process is loading.
  final String loadingStatusText;

  /// Status text to show when the processing is failed.
  final String failedStatusText;

  /// Status text to show when there's no more items to load.
  final String finishedStatusText;

  /// Manually turn-off the next load more.
  ///
  /// Set this to `true` to set the load more as `finished` (no more items). Default is `false`.
  ///
  /// The use-case is when there's no more items to load, you might want `MasonryLoadMore` to not running again.
  final bool isFinished;

  /// Whether or not to run the load more even though the result is empty/finished.
  final bool runOnEmptyResult;

  /// Callback function to run during the load more process.
  ///
  /// To mark the status as success or delay, set the return to `true`.
  ///
  /// To mark the status as failed, set the return to `false`.
  final FutureCallBack onLoadMore;

  final ScrollPhysics? physics;
  final EdgeInsets? padding;
  final int crossAxisCount;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final int itemsCount;
  final Function(BuildContext, int) itemBuilder;

  const MasonryLoadMore({
    super.key,
    this.masonryKey,
    this.scrollController,
    this.loadingWidgetContainerHeight =
        MasonryLoadMoreLoadingWidgetDefaultOpts.containerHeight,
    this.loadingWidgetSize = MasonryLoadMoreLoadingWidgetDefaultOpts.size,
    this.loadingWidgetStrokeWidth =
        MasonryLoadMoreLoadingWidgetDefaultOpts.strokeWidth,
    this.loadingWidgetColor = MasonryLoadMoreLoadingWidgetDefaultOpts.color,
    this.loadingWidgetAnimationDelay =
        MasonryLoadMoreLoadingWidgetDefaultOpts.delay,
    this.idleStatusText = MasonryLoadMoreStatusText.idle,
    this.loadingStatusText = MasonryLoadMoreStatusText.loading,
    this.failedStatusText = MasonryLoadMoreStatusText.failed,
    this.finishedStatusText = MasonryLoadMoreStatusText.finished,
    this.isFinished = false,
    this.runOnEmptyResult = false,
    required this.onLoadMore,
    this.physics,
    this.padding,
    required this.crossAxisCount,
    required this.mainAxisSpacing,
    required this.crossAxisSpacing,
    required this.itemsCount,
    required this.itemBuilder,
  });

  @override
  MasonryLoadMoreState createState() => MasonryLoadMoreState();
}

class MasonryLoadMoreState extends State<MasonryLoadMore> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _buildMasonry();
  }

  // Widget _buildMasonryWithLoading() {
  //   return Column(
  //     children: [
  //       Expanded(
  //         child: _buildMasonry(),
  //       ),
  //       _buildLoadMoreView(),
  //     ],
  //   );
  // }

  Widget _buildMasonry() {
    if (widget.itemsCount == 0 && widget.runOnEmptyResult) {
      return _buildLoadMoreView();
    }

    return MasonryGridView.count(
      key: widget.masonryKey,
      controller: widget.scrollController,
      physics: widget.physics,
      // cacheExtent: 5000,
      padding: widget.padding,
      crossAxisCount: widget.crossAxisCount,
      mainAxisSpacing: widget.mainAxisSpacing,
      crossAxisSpacing: widget.crossAxisSpacing,
      itemBuilder: (context, index) {
        return index < widget.itemsCount - 1
            ? widget.itemBuilder(context, index)
            : Column(
                children: [
                  widget.itemBuilder(context, index),
                  _buildLoadMoreView(),
                ],
              );
      },
      itemCount: widget.itemsCount,
    );
  }

  MasonryLoadMoreStatusState status = MasonryLoadMoreStatusState.idle;

  Widget _buildLoadMoreView() {
    if (widget.isFinished == true) {
      status = MasonryLoadMoreStatusState.finished;
    } else {
      if (status == MasonryLoadMoreStatusState.finished) {
        status = MasonryLoadMoreStatusState.idle;
      }
    }

    return NotificationListener<_RetryNotification>(
      onNotification: _onRetry,
      child: NotificationListener<_BuildNotification>(
        onNotification: _onLoadMoreBuild,
        child: MasonryLoadMoreView(
          status: status,
          containerHeight: widget.loadingWidgetContainerHeight,
          size: widget.loadingWidgetSize,
          strokeWidth: widget.loadingWidgetStrokeWidth,
          color: widget.loadingWidgetColor,
          animationDelay: widget.loadingWidgetAnimationDelay,
          idleStatusText: widget.idleStatusText,
          loadingStatusText: widget.loadingStatusText,
          failedStatusText: widget.failedStatusText,
          finishedStatusText: widget.finishedStatusText,
        ),
      ),
    );
  }

  bool _onLoadMoreBuild(_BuildNotification notification) {
    if (status == MasonryLoadMoreStatusState.idle) {
      loadMore();
    }

    if (status == MasonryLoadMoreStatusState.loading) {
      return false;
    }

    if (status == MasonryLoadMoreStatusState.failed) {
      return false;
    }

    if (status == MasonryLoadMoreStatusState.finished) {
      return false;
    }

    return false;
  }

  void _updateStatus(MasonryLoadMoreStatusState status) {
    if (mounted) setState(() => this.status = status);
  }

  bool _onRetry(_RetryNotification notification) {
    loadMore();
    return false;
  }

  void loadMore() {
    _updateStatus(MasonryLoadMoreStatusState.loading);

    widget.onLoadMore().then((v) {
      if (v == true) {
        _updateStatus(MasonryLoadMoreStatusState.idle);
      } else {
        _updateStatus(MasonryLoadMoreStatusState.failed);
      }
    });
  }
}

class MasonryLoadMoreView extends StatefulWidget {
  final MasonryLoadMoreStatusState status;

  final double containerHeight;
  final double size;
  final double strokeWidth;
  final Color color;
  final int animationDelay;

  final String idleStatusText;
  final String loadingStatusText;
  final String failedStatusText;
  final String finishedStatusText;

  const MasonryLoadMoreView({
    super.key,
    required this.status,
    required this.containerHeight,
    required this.size,
    required this.strokeWidth,
    required this.color,
    required this.animationDelay,
    required this.idleStatusText,
    required this.loadingStatusText,
    required this.failedStatusText,
    required this.finishedStatusText,
  });

  @override
  State<MasonryLoadMoreView> createState() => _MasonryLoadMoreViewState();
}

class _MasonryLoadMoreViewState extends State<MasonryLoadMoreView> {
  final buildNotification = _BuildNotification();
  final retryNotification = _RetryNotification();

  @override
  Widget build(BuildContext context) {
    notify();
    return const SizedBox(height: 1.0);

    // return GestureDetector(
    //   behavior: HitTestBehavior.translucent,
    //   onTap: () {
    //     if (widget.status == MasonryLoadMoreStatusState.failed ||
    //         widget.status == MasonryLoadMoreStatusState.idle) {
    //       _notifyRetryProcess();
    //     }
    //   },
    //   child: Container(
    //     height: widget.containerHeight,
    //     alignment: Alignment.center,
    //     child: buildTextWidget(),
    //   ),
    // );
  }

  Widget buildTextWidget() {
    String text = '';

    switch (widget.status) {
      case MasonryLoadMoreStatusState.idle:
        text = widget.idleStatusText;
        break;
      case MasonryLoadMoreStatusState.loading:
        text = widget.loadingStatusText;
        break;
      case MasonryLoadMoreStatusState.failed:
        text = widget.failedStatusText;
        break;
      case MasonryLoadMoreStatusState.finished:
        text = widget.finishedStatusText;
        break;
    }

    if (widget.status == MasonryLoadMoreStatusState.failed) {
      return Container(padding: const EdgeInsets.all(0.0), child: Text(text));
    }

    if (widget.status == MasonryLoadMoreStatusState.idle) {
      return Text(text);
    }

    if (widget.status == MasonryLoadMoreStatusState.loading) {
      return Container(
        alignment: Alignment.center,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            SizedBox(
              width: widget.size,
              height: widget.size,
              child: CircularProgressIndicator(
                strokeWidth: widget.strokeWidth,
                valueColor: AlwaysStoppedAnimation<Color>(widget.color),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 10.0),
              child: Text(text),
            ),
          ],
        ),
      );
    }

    if (widget.status == MasonryLoadMoreStatusState.finished) {
      return Text(text);
    }

    return Text(text);
  }

  void notify() async {
    Duration delay = max(
      Duration(microseconds: widget.animationDelay),
      const Duration(
        milliseconds: MasonryLoadMoreLoadingWidgetDefaultOpts.delay,
      ),
    );

    await Future.delayed(delay);

    if (widget.status == MasonryLoadMoreStatusState.idle) {
      _notifyBuildProcess();
    }
  }

  Duration max(Duration duration, Duration duration2) {
    if (duration > duration2) {
      return duration;
    }
    return duration2;
  }

  void _notifyBuildProcess() {
    if (!mounted) return;
    buildNotification.dispatch(context);
  }

  // void _notifyRetryProcess() {
  //   if (!mounted) return;
  //   retryNotification.dispatch(context);
  // }
}
