import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';

final class BlockedArtistIdsNotifier extends IdListNotifier {}

final blockedArtistIdsProvider =
    NotifierProvider.autoDispose<BlockedArtistIdsNotifier, List<int>>(
      BlockedArtistIdsNotifier.new,
    );

final blockedArtistProvider = Provider.autoDispose<List<ArtistData>>((ref) {
  final ids = ref.watch(blockedArtistIdsProvider);
  final store = ref.watch(artistStoreProvider);
  return ids.map((id) => store[id]).whereType<ArtistData>().toList();
});
