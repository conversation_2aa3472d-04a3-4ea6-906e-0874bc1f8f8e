import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/widgets/pm_network_image.dart';
import 'package:portraitmode/appbar/avatar.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/dto/artist_partial_data.dart';
import 'package:portraitmode/artist/widgets/artist_detail_screen.dart';

class ArtistListSlider extends StatefulWidget {
  const ArtistListSlider({super.key, this.artistList = const []});

  final List<ArtistData> artistList;

  @override
  ArtistListSliderState createState() => ArtistListSliderState();
}

class ArtistListSliderState extends State<ArtistListSlider> {
  final double _sliderItemGap = 13.0;
  final double _heightReducer = 0.0;

  late double _sliderItemHeight;
  late int _totalItems;

  @override
  void initState() {
    super.initState();
    _totalItems = widget.artistList.length;
  }

  late double _sliderItemWidth;

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.sizeOf(context).width;

    _sliderItemWidth = (screenWidth / 1.3) - _sliderItemGap;

    double itemHeight =
        _sliderItemWidth - (_sliderItemWidth / 1.5) - _heightReducer;

    _sliderItemHeight = (itemHeight * 1.8) + _sliderItemGap;

    return Container(
      padding: const EdgeInsets.only(top: 20, bottom: 22),
      decoration: BoxDecoration(color: context.colors.baseColorAlt),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.only(bottom: 10.0, left: 13.0, right: 13.0),
            child: Text(
              "Discover artists",
              style: TextStyle(
                height: 1,
                fontSize: 13,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          SizedBox(
            height: _sliderItemHeight,
            child: ListView.builder(
              cacheExtent: 3000,
              scrollDirection: Axis.horizontal,
              itemCount: _totalItems,
              itemBuilder: (BuildContext context, int index) {
                // Check if photoList[index] exists.
                if (index >= widget.artistList.length) {
                  return const SizedBox.shrink();
                }

                return _buildItem(widget.artistList[index], index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItem(ArtistData artist, int index) {
    return GestureDetector(
      onTap: () => _handleArtistTap(context, artist),
      child: Container(
        margin: EdgeInsets.only(
          left: _sliderItemGap,
          right: index == _totalItems - 1 ? _sliderItemGap : 0,
        ),
        width: _sliderItemWidth,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(PhotoStyleConfig.borderRadius),
          image: DecorationImage(
            image: PmNetworkImageProvider(artist.latestPhotoUrl).imageProvider,
            fit: BoxFit.cover,
          ),
        ),
        child: Stack(
          children: [
            Positioned(
              bottom: 10,
              right: 10,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(
                        24.0,
                      ), // Half of the avatar size
                      border: Border.all(color: Colors.white, width: 1.0),
                    ),
                    child: Avatar(
                      imageUrl: artist.avatarUrl,
                      size: 48.0,
                      useBorder: false,
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    artist.displayName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      overflow: TextOverflow.ellipsis,
                      shadows: [
                        Shadow(
                          offset: Offset(1.0, 1.0),
                          blurRadius: 40.0,
                          color: Colors.black,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleArtistTap(BuildContext context, ArtistData artist) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ArtistDetailScreen(
          partialData: ArtistPartialData(
            id: artist.id,
            nicename: artist.nicename,
            displayName: artist.displayName,
            profileUrl: artist.profileUrl,
            avatarUrl: artist.avatarUrl,
            membershipType: artist.membershipType,
          ),
        ),
      ),
    );
  }
}
